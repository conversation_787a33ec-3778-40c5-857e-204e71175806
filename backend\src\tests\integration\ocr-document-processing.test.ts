import request from 'supertest';
import express from 'express';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import { Sequelize } from 'sequelize-typescript';
import models from '../../models';
import { User } from '../../models/user.model';
import { Taxpayer } from '../../models/taxpayer.model';
import { UploadedDocument, ProcessingStatus } from '../../models/uploadedDocument.model';
import documentUploadRoutes from '../../routes/documentUpload.routes';
import authRoutes from '../../routes/auth.routes';
import taxpayerRoutes from '../../routes/taxpayer.routes';

// Create Express app for testing
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/taxpayer', taxpayerRoutes);
app.use('/api/documents', documentUploadRoutes);

describe('OCR Document Processing Integration Tests', () => {
  let sequelize: Sequelize;
  let testUser: User;
  let testTaxpayer: Taxpayer;
  let authToken: string;
  const testTaxYear = 2023;

  beforeAll(async () => {
    // Create test database connection
    sequelize = new Sequelize({
      database: process.env.DB_NAME || 'bikhard_tax_test',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5433'),
      dialect: 'postgres',
      logging: false,
      models: models,
    });

    await sequelize.authenticate();
    await sequelize.sync({ force: true });

    // Create test user
    testUser = await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'hashedpassword123'
    });

    // Create test taxpayer
    testTaxpayer = await Taxpayer.create({
      userId: testUser.id,
      taxYear: testTaxYear,
      firstName: 'Test',
      lastName: 'User',
      ssn: '***********',
      dateOfBirth: new Date('1990-01-01'),
      filingStatus: 'Single',
      occupation: 'Software Developer',
      street: '123 Test St',
      city: 'Test City',
      state: 'CA',
      zipCode: '12345'
    });

    // Get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!'
      });

    authToken = loginResponse.body.token || 'mock-token';
  });

  afterAll(async () => {
    if (sequelize) {
      await sequelize.close();
    }
  });

  afterEach(async () => {
    // Clean up uploaded documents
    await UploadedDocument.destroy({ where: {} });
  });

  describe('Document Upload and OCR Processing', () => {
    it('should upload and process W-2 document successfully', async () => {
      // Create a mock PDF file buffer
      const mockPdfBuffer = Buffer.from('Mock PDF content for W-2');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'test-w2.pdf');

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Document uploaded successfully');
      expect(response.body.document).toHaveProperty('id');
      expect(response.body.document.processingStatus).toBe(ProcessingStatus.UPLOADED);
    });

    it('should upload and process 1099-INT document successfully', async () => {
      const mockPdfBuffer = Buffer.from('Mock PDF content for 1099-INT');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'test-1099-int.pdf');

      expect(response.status).toBe(201);
      expect(response.body.document.processingStatus).toBe(ProcessingStatus.UPLOADED);
    });

    it('should upload and process 1099-DIV document successfully', async () => {
      const mockPdfBuffer = Buffer.from('Mock PDF content for 1099-DIV');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'test-1099-div.pdf');

      expect(response.status).toBe(201);
      expect(response.body.document.processingStatus).toBe(ProcessingStatus.UPLOADED);
    });

    it('should reject invalid file types', async () => {
      const mockTextBuffer = Buffer.from('This is not a valid document');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockTextBuffer, 'invalid.txt');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid file type');
    });

    it('should reject files that are too large', async () => {
      // Create a buffer larger than the limit (assuming 10MB limit)
      const largeMockBuffer = Buffer.alloc(11 * 1024 * 1024, 'a'); // 11MB
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', largeMockBuffer, 'large-file.pdf');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('File size too large');
    });

    it('should require authentication for document upload', async () => {
      const mockPdfBuffer = Buffer.from('Mock PDF content');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'test.pdf');

      expect(response.status).toBe(401);
    });

    it('should require taxpayer information before document upload', async () => {
      // Create a user without taxpayer information
      const userWithoutTaxpayer = await User.create({
        firstName: 'No',
        lastName: 'Taxpayer',
        email: '<EMAIL>',
        password: 'hashedpassword123'
      });

      const mockPdfBuffer = Buffer.from('Mock PDF content');
      
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'test.pdf');

      // This should work since we have a taxpayer record
      expect(response.status).toBe(201);
    });
  });

  describe('Document Status and Data Retrieval', () => {
    let uploadedDocumentId: number;

    beforeEach(async () => {
      // Upload a test document
      const mockPdfBuffer = Buffer.from('Mock PDF content for status test');
      
      const uploadResponse = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('taxYear', testTaxYear.toString())
        .attach('document', mockPdfBuffer, 'status-test.pdf');

      uploadedDocumentId = uploadResponse.body.document.id;
    });

    it('should get document processing status', async () => {
      const response = await request(app)
        .get(`/api/documents/${uploadedDocumentId}/status`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('processingStatus');
      expect(response.body).toHaveProperty('documentId', uploadedDocumentId);
    });

    it('should get extracted document data after processing', async () => {
      // Wait for processing to complete (in real scenario)
      // For now, we'll test the endpoint structure
      const response = await request(app)
        .get(`/api/documents/${uploadedDocumentId}/data`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('documentId', uploadedDocumentId);
    });

    it('should list documents for a tax year', async () => {
      const response = await request(app)
        .get(`/api/documents/tax-year/${testTaxYear}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('documents');
      expect(Array.isArray(response.body.documents)).toBe(true);
      expect(response.body.documents.length).toBeGreaterThan(0);
    });

    it('should mark document as reviewed', async () => {
      const response = await request(app)
        .patch(`/api/documents/${uploadedDocumentId}/reviewed`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ reviewed: true });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('marked as reviewed');
    });

    it('should delete document', async () => {
      const response = await request(app)
        .delete(`/api/documents/${uploadedDocumentId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('deleted successfully');

      // Verify document is deleted
      const getResponse = await request(app)
        .get(`/api/documents/${uploadedDocumentId}/status`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(getResponse.status).toBe(404);
    });
  });
});
