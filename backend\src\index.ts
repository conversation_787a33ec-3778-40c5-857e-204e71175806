import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { connectDB } from './config/database';

// Import routes
import authRoutes from './routes/auth.routes';
import taxpayerRoutes from './routes/taxpayer.routes';
import w2Routes from './routes/w2.routes';
import taxCalculationRoutes from './routes/taxCalculation.routes';
import form1099intRoutes from './routes/form1099int.routes';
import form1099divRoutes from './routes/form1099div.routes';
import scheduleCRoutes from './routes/scheduleC.routes';
import adjustmentsRoutes from './routes/adjustments.routes';
import scheduleARoutes from './routes/scheduleA.routes';
import dependentRoutes from './routes/dependent.routes';
import childTaxCreditRoutes from './routes/childTaxCredit.routes';
import earnedIncomeTaxCreditRoutes from './routes/earnedIncomeTaxCredit.routes';
import estimatedTaxPaymentRoutes from './routes/estimatedTaxPayment.routes';
import documentUploadRoutes from './routes/documentUpload.routes';

// Load environment variables
dotenv.config();

// Initialize Express app
const app: Express = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:3000',  // React dev server
    'http://localhost:80',    // Docker frontend
    'http://bikhard-frontend', // Docker container name
    'http://frontend',        // Docker compose service name
    process.env.FRONTEND_URL  // Environment variable for production
  ].filter(Boolean), // Remove undefined values
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  console.log('Request body:', req.body);
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/taxpayer', taxpayerRoutes);
app.use('/api/w2', w2Routes);
app.use('/api/tax-calculation', taxCalculationRoutes);
app.use('/api/form1099int', form1099intRoutes);
app.use('/api/form1099div', form1099divRoutes);
app.use('/api/schedule-c', scheduleCRoutes);
app.use('/api/adjustments', adjustmentsRoutes);
app.use('/api/schedule-a', scheduleARoutes);
app.use('/api/dependent', dependentRoutes);
app.use('/api/child-tax-credit', childTaxCreditRoutes);
app.use('/api/earned-income-tax-credit', earnedIncomeTaxCreditRoutes);
app.use('/api/estimated-tax-payment', estimatedTaxPaymentRoutes);
app.use('/api/documents', documentUploadRoutes);

// Default route
app.get('/', (req: Request, res: Response) => {
  res.send('BikHard USA Tax Filing API');
});

// Health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    message: 'API server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API documentation endpoint
app.get('/api', (req: Request, res: Response) => {
  res.json({
    message: 'BikHard USA Tax Filing API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      taxpayer: '/api/taxpayer',
      w2: '/api/w2',
      taxCalculation: '/api/tax-calculation',
      form1099int: '/api/form1099int',
      form1099div: '/api/form1099div',
      scheduleC: '/api/schedule-c',
      adjustments: '/api/adjustments',
      scheduleA: '/api/schedule-a',
      dependents: '/api/dependent',
      childTaxCredit: '/api/child-tax-credit',
      earnedIncomeTaxCredit: '/api/earned-income-tax-credit',
      estimatedTaxPayments: '/api/estimated-tax-payment',
      documents: '/api/documents',
      health: '/api/health'
    }
  });
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: Function) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Start server
const startServer = async () => {
  try {
    // Connect to database first
    await connectDB();

    // Start listening for requests
    app.listen(port, () => {
      console.log(`Server is running on port ${port}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
